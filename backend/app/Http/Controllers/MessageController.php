<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use App\Jobs\EchoMessageBack;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class MessageController extends BaseController
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Send a new message.
     */
    public function send(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        // Pass both the message and the authenticated user
        EchoMessageBack::dispatch($request->message, $request->user());

        return response()->json(['status' => 'Message sent successfully']);
    }
}
